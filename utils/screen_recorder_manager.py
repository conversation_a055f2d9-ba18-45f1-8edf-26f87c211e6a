"""
录屏管理工具
提供测试用例录屏功能，支持自动录屏、文件管理和Allure报告集成
"""
import os
import time
import subprocess
import inspect
from typing import Optional, Dict, Any
from datetime import datetime
from pathlib import Path
from core.logger import log
from utils.file_utils import FileUtils
from utils.yaml_utils import YamlUtils


class ScreenRecorderManager:
    """录屏管理器"""
    
    def __init__(self):
        """初始化录屏管理器"""
        self.config = self._load_config()
        self.base_recording_dir = self.config.get("app", {}).get("recording_path", "reports/recordings")
        self.project_root = YamlUtils.get_project_root()

        # 录屏配置
        recording_config = self.config.get("recording", {})
        self.device_recording_dir = recording_config.get("device_recording_dir", "/sdcard/Movies/ScreenRecord")
        self.recording_enabled = recording_config.get("enabled", True)
        self.default_quality = recording_config.get("default_quality", "medium")
        self.quality_settings = recording_config.get("quality", {
            "low": {"video_size": "720x1280", "bit_rate": "4000000"},
            "medium": {"video_size": "1080x1920", "bit_rate": "6000000"},
            "high": {"video_size": "1080x1920", "bit_rate": "8000000"}
        })

        # 运行时状态
        self.current_recording_file = None
        self.recording_process = None
        self.is_recording = False
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_path = YamlUtils.get_config_path("config.yaml")
            return YamlUtils.load_yaml(config_path)
        except Exception as e:
            log.warning(f"加载录屏配置失败: {e}")
            return {}
    
    def get_test_context(self) -> Dict[str, str]:
        """
        获取测试上下文信息
        
        Returns:
            Dict: 包含测试类名、测试方法名等信息
        """
        context = {
            "test_class": "unknown",
            "test_method": "unknown",
            "test_file": "unknown"
        }
        
        try:
            # 获取调用栈
            for frame_info in inspect.stack():
                frame = frame_info.frame
                function_name = frame_info.function
                filename = frame_info.filename
                
                # 跳过当前文件和框架文件
                if 'screen_recorder_manager.py' in filename or 'conftest.py' in filename:
                    continue
                
                # 查找测试类实例
                if 'self' in frame.f_locals:
                    test_instance = frame.f_locals['self']
                    class_name = test_instance.__class__.__name__
                    if class_name.startswith('Test'):
                        context["test_class"] = class_name
                        if function_name.startswith('test_'):
                            context["test_method"] = function_name
                        context["test_file"] = os.path.basename(filename)
                        break
                
                # 查找测试函数
                if function_name.startswith('test_'):
                    context["test_method"] = function_name
                    context["test_file"] = os.path.basename(filename)
                    
                    # 从文件名推断测试类名
                    if 'test_' in filename:
                        base_name = os.path.basename(filename)
                        if base_name.startswith('test_'):
                            name_part = base_name.replace('test_', '').replace('.py', '')
                            context["test_class"] = f"Test{name_part.title()}"
                    break
            
            # 尝试从pytest获取信息
            try:
                import pytest
                if hasattr(pytest, 'current_item') and pytest.current_item:
                    item = pytest.current_item
                    if hasattr(item, 'cls') and item.cls:
                        context["test_class"] = item.cls.__name__
                    if hasattr(item, 'name'):
                        context["test_method"] = item.name
                    if hasattr(item, 'fspath'):
                        context["test_file"] = os.path.basename(str(item.fspath))
            except ImportError:
                pass
                
        except Exception as e:
            log.debug(f"获取测试上下文失败: {e}")
        
        return context
    
    def get_recording_directory(self, test_class: Optional[str] = None) -> str:
        """
        获取录屏目录
        
        Args:
            test_class: 测试类名称，为None时自动获取
            
        Returns:
            str: 录屏目录完整路径
        """
        if test_class is None:
            context = self.get_test_context()
            test_class = context["test_class"]
        
        # 创建按测试类分组的目录结构
        recording_dir = os.path.join(self.project_root, self.base_recording_dir, test_class)
        FileUtils.ensure_dir(recording_dir)
        
        return recording_dir
    
    def generate_recording_filename(self, prefix: str = "recording", 
                                   include_test_method: bool = True) -> str:
        """
        生成录屏文件名
        
        Args:
            prefix: 文件名前缀
            include_test_method: 是否包含测试方法名
            
        Returns:
            str: 录屏文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # 包含毫秒
        
        if include_test_method:
            context = self.get_test_context()
            test_method = context["test_method"]
            if test_method != "unknown":
                return f"{prefix}_{test_method}_{timestamp}.mp4"
        
        return f"{prefix}_{timestamp}.mp4"
    
    def _run_adb_command(self, command: list, timeout: int = 30) -> tuple:
        """
        执行ADB命令
        
        Args:
            command: ADB命令列表
            timeout: 超时时间
            
        Returns:
            tuple: (success, stdout, stderr)
        """
        try:
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            log.error(f"ADB命令超时: {' '.join(command)}")
            return False, "", "命令超时"
        except Exception as e:
            log.error(f"执行ADB命令失败: {e}")
            return False, "", str(e)
    
    def _ensure_device_recording_dir(self) -> bool:
        """确保设备录屏目录存在"""
        try:
            command = ["adb", "shell", "mkdir", "-p", self.device_recording_dir]
            success, stdout, stderr = self._run_adb_command(command)
            
            if success:
                log.debug(f"设备录屏目录已确保存在: {self.device_recording_dir}")
                return True
            else:
                log.warning(f"创建设备录屏目录失败: {stderr}")
                return False
                
        except Exception as e:
            log.error(f"确保设备录屏目录异常: {e}")
            return False
    
    def start_recording(self, filename: Optional[str] = None,
                       quality: str = None,
                       video_size: str = None,
                       bit_rate: str = None) -> bool:
        """
        开始录屏

        Args:
            filename: 录屏文件名，为None时自动生成
            quality: 录屏质量等级 (low/medium/high)，为None时使用默认设置
            video_size: 视频分辨率，为None时根据quality自动设置
            bit_rate: 比特率，为None时根据quality自动设置

        Returns:
            bool: 是否成功开始录屏
        """
        try:
            if self.is_recording:
                log.warning("录屏已在进行中，跳过重复开始")
                return True

            # 检查录屏是否启用
            if not self.recording_enabled:
                log.info("录屏功能已禁用")
                return True

            # 确定录屏质量设置
            if quality is None:
                quality = self.default_quality

            quality_config = self.quality_settings.get(quality, self.quality_settings[self.default_quality])

            if video_size is None:
                video_size = quality_config["video_size"]
            if bit_rate is None:
                bit_rate = quality_config["bit_rate"]

            log.info(f"录屏质量设置: {quality} (分辨率: {video_size}, 比特率: {bit_rate})")

            # 生成文件名
            if filename is None:
                filename = self.generate_recording_filename()

            # 确保设备录屏目录存在
            if not self._ensure_device_recording_dir():
                log.error("无法创建设备录屏目录")
                return False
            
            # 设备上的录屏文件路径
            device_file_path = f"{self.device_recording_dir}/{filename}"
            
            # 构建录屏命令
            command = [
                "adb", "shell", "screenrecord",
                "--size", video_size,
                "--bit-rate", bit_rate,
                device_file_path
            ]
            
            log.info(f"开始录屏: {filename}")
            log.debug(f"录屏命令: {' '.join(command)}")
            
            # 启动录屏进程
            self.recording_process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一小段时间确保录屏开始
            time.sleep(1)
            
            # 检查进程是否正常启动
            if self.recording_process.poll() is None:
                self.current_recording_file = device_file_path
                self.is_recording = True
                log.info(f"✅ 录屏开始成功: {filename}")
                return True
            else:
                stdout, stderr = self.recording_process.communicate()
                log.error(f"录屏启动失败: {stderr}")
                self.recording_process = None
                return False
                
        except Exception as e:
            log.error(f"开始录屏异常: {e}")
            return False
    
    def stop_recording(self) -> Optional[str]:
        """
        停止录屏
        
        Returns:
            Optional[str]: 本地录屏文件路径，失败时返回None
        """
        try:
            if not self.is_recording or not self.recording_process:
                log.warning("当前没有进行中的录屏")
                return None
            
            log.info("停止录屏...")
            
            # 发送中断信号停止录屏
            self.recording_process.terminate()
            
            # 等待进程结束
            try:
                stdout, stderr = self.recording_process.communicate(timeout=10)
                log.debug(f"录屏进程输出: {stdout}")
                if stderr:
                    log.debug(f"录屏进程错误: {stderr}")
            except subprocess.TimeoutExpired:
                log.warning("录屏进程未在预期时间内结束，强制终止")
                self.recording_process.kill()
                self.recording_process.communicate()
            
            # 等待文件写入完成
            time.sleep(2)
            
            # 拉取录屏文件到本地
            local_file_path = self._pull_recording_file()
            
            # 清理状态
            self.recording_process = None
            self.is_recording = False
            device_file = self.current_recording_file
            self.current_recording_file = None
            
            # 删除设备上的录屏文件
            if device_file:
                self._cleanup_device_file(device_file)
            
            log.info(f"✅ 录屏停止完成")
            return local_file_path
            
        except Exception as e:
            log.error(f"停止录屏异常: {e}")
            return None
    
    def _pull_recording_file(self) -> Optional[str]:
        """
        从设备拉取录屏文件到本地
        
        Returns:
            Optional[str]: 本地文件路径，失败时返回None
        """
        try:
            if not self.current_recording_file:
                log.error("没有当前录屏文件信息")
                return None
            
            # 获取本地录屏目录
            local_recording_dir = self.get_recording_directory()
            
            # 生成本地文件路径
            filename = os.path.basename(self.current_recording_file)
            local_file_path = os.path.join(local_recording_dir, filename)
            
            # 拉取文件
            command = ["adb", "pull", self.current_recording_file, local_file_path]
            success, stdout, stderr = self._run_adb_command(command)
            
            if success:
                # 验证文件是否存在且有内容
                if os.path.exists(local_file_path) and os.path.getsize(local_file_path) > 0:
                    log.info(f"录屏文件拉取成功: {local_file_path}")
                    return local_file_path
                else:
                    log.error(f"拉取的录屏文件无效: {local_file_path}")
                    return None
            else:
                log.error(f"拉取录屏文件失败: {stderr}")
                return None
                
        except Exception as e:
            log.error(f"拉取录屏文件异常: {e}")
            return None
    
    def _cleanup_device_file(self, device_file_path: str) -> bool:
        """
        清理设备上的录屏文件
        
        Args:
            device_file_path: 设备文件路径
            
        Returns:
            bool: 是否清理成功
        """
        try:
            command = ["adb", "shell", "rm", device_file_path]
            success, stdout, stderr = self._run_adb_command(command)
            
            if success:
                log.debug(f"设备录屏文件清理成功: {device_file_path}")
                return True
            else:
                log.warning(f"设备录屏文件清理失败: {stderr}")
                return False
                
        except Exception as e:
            log.error(f"清理设备录屏文件异常: {e}")
            return False


    def attach_to_allure(self, recording_file_path: str,
                        name: str = "测试录屏") -> bool:
        """
        将录屏文件附加到Allure报告

        Args:
            recording_file_path: 录屏文件路径
            name: 附件名称

        Returns:
            bool: 是否成功附加
        """
        try:
            if not os.path.exists(recording_file_path):
                log.error(f"录屏文件不存在: {recording_file_path}")
                return False

            # 导入allure模块
            try:
                import allure

                # 附加录屏文件到Allure报告
                allure.attach.file(
                    recording_file_path,
                    name=name,
                    attachment_type=allure.attachment_type.MP4
                )

                log.info(f"录屏文件已附加到Allure报告: {name}")
                return True

            except ImportError:
                log.warning("Allure模块未安装，跳过录屏附加")
                return False

        except Exception as e:
            log.error(f"附加录屏到Allure报告失败: {e}")
            return False

    def cleanup_old_recordings(self, days: int = 7) -> None:
        """
        清理旧录屏文件

        Args:
            days: 保留天数
        """
        try:
            import time
            from pathlib import Path

            cutoff_time = time.time() - (days * 24 * 60 * 60)
            base_dir = os.path.join(self.project_root, self.base_recording_dir)

            if not os.path.exists(base_dir):
                return

            deleted_count = 0
            for root, dirs, files in os.walk(base_dir):
                for file in files:
                    if file.endswith('.mp4'):
                        file_path = os.path.join(root, file)
                        if os.path.getmtime(file_path) < cutoff_time:
                            os.remove(file_path)
                            deleted_count += 1

            log.info(f"清理了 {deleted_count} 个超过 {days} 天的录屏文件")

        except Exception as e:
            log.error(f"清理旧录屏文件失败: {e}")

    def get_recording_summary(self) -> Dict[str, Any]:
        """
        获取录屏统计信息

        Returns:
            Dict: 录屏统计信息
        """
        try:
            base_dir = os.path.join(self.project_root, self.base_recording_dir)

            if not os.path.exists(base_dir):
                return {"total_files": 0, "test_classes": [], "total_size": 0}

            summary = {
                "total_files": 0,
                "test_classes": [],
                "total_size": 0,
                "by_class": {}
            }

            for item in os.listdir(base_dir):
                item_path = os.path.join(base_dir, item)
                if os.path.isdir(item_path):
                    # 这是一个测试类目录
                    class_info = {
                        "name": item,
                        "file_count": 0,
                        "size": 0
                    }

                    for file in os.listdir(item_path):
                        if file.endswith('.mp4'):
                            file_path = os.path.join(item_path, file)
                            file_size = os.path.getsize(file_path)
                            class_info["file_count"] += 1
                            class_info["size"] += file_size

                    summary["test_classes"].append(item)
                    summary["by_class"][item] = class_info
                    summary["total_files"] += class_info["file_count"]
                    summary["total_size"] += class_info["size"]

                elif item.endswith('.mp4'):
                    # 根目录下的录屏文件
                    file_size = os.path.getsize(item_path)
                    summary["total_files"] += 1
                    summary["total_size"] += file_size

            return summary

        except Exception as e:
            log.error(f"获取录屏统计失败: {e}")
            return {"total_files": 0, "test_classes": [], "total_size": 0}

    def is_recording_active(self) -> bool:
        """
        检查是否有录屏正在进行

        Returns:
            bool: 是否正在录屏
        """
        return self.is_recording and self.recording_process is not None

    def force_stop_recording(self) -> bool:
        """
        强制停止录屏（不保存文件）

        Returns:
            bool: 是否成功停止
        """
        try:
            if not self.is_recording or not self.recording_process:
                return True

            log.warning("强制停止录屏...")

            # 强制终止进程
            self.recording_process.kill()
            self.recording_process.communicate()

            # 清理设备文件
            if self.current_recording_file:
                self._cleanup_device_file(self.current_recording_file)

            # 重置状态
            self.recording_process = None
            self.is_recording = False
            self.current_recording_file = None

            log.info("录屏已强制停止")
            return True

        except Exception as e:
            log.error(f"强制停止录屏异常: {e}")
            return False


# 全局录屏管理器实例
screen_recorder_manager = ScreenRecorderManager()
